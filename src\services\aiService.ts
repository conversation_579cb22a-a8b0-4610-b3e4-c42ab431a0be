// AI服务接口

export interface AIGenerateRequest {
  content: string // 当前节点的内容
  type: 'mindmap' // 生成类型
  options?: {
    maxNodes?: number // 最大生成节点数
    depth?: number // 生成深度
    language?: string // 语言
  }
}

export interface AIGenerateResponse {
  success: boolean
  data?: {
    nodes: Array<{
      text: string
      level: number
      children?: Array<{
        text: string
        level: number
      }>
    }>
  }
  error?: string
  message?: string
}

// AI服务类
export class AIService {
  private baseURL: string
  
  constructor(baseURL: string = 'http://localhost:3001') {
    this.baseURL = baseURL
  }

  /**
   * 调用AI生成思维导图内容
   * @param request AI生成请求
   * @returns AI生成响应
   */
  async generateMindMap(request: AIGenerateRequest): Promise<AIGenerateResponse> {
    try {
      // TODO: 这里预留后端接口调用位置
      // 当后端Koa框架实现后，取消注释以下代码：
      
      /*
      const response = await fetch(`${this.baseURL}/api/ai/generate-mindmap`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data: AIGenerateResponse = await response.json()
      return data
      */

      // 临时模拟数据，用于前端开发和测试
      return this.getMockResponse(request)
      
    } catch (error) {
      console.error('AI生成失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误',
        message: 'AI服务暂时不可用，请稍后重试'
      }
    }
  }

  /**
   * 模拟AI响应数据（用于开发测试）
   * @param request 请求参数
   * @returns 模拟响应
   */
  private getMockResponse(request: AIGenerateRequest): AIGenerateResponse {
    // 根据输入内容生成相关的思维导图节点
    const content = request.content.toLowerCase()
    
    let mockNodes: Array<{
      text: string
      level: number
      children?: Array<{
        text: string
        level: number
      }>
    }> = []

    // 根据不同的内容类型生成不同的模拟数据
    if (content.includes('项目') || content.includes('计划')) {
      mockNodes = [
        {
          text: '项目规划',
          level: 1,
          children: [
            { text: '需求分析', level: 2 },
            { text: '技术选型', level: 2 },
            { text: '时间安排', level: 2 }
          ]
        },
        {
          text: '团队协作',
          level: 1,
          children: [
            { text: '角色分工', level: 2 },
            { text: '沟通机制', level: 2 }
          ]
        },
        {
          text: '风险管理',
          level: 1,
          children: [
            { text: '风险识别', level: 2 },
            { text: '应对策略', level: 2 }
          ]
        }
      ]
    } else if (content.includes('学习') || content.includes('教育')) {
      mockNodes = [
        {
          text: '学习目标',
          level: 1,
          children: [
            { text: '知识点梳理', level: 2 },
            { text: '技能提升', level: 2 }
          ]
        },
        {
          text: '学习方法',
          level: 1,
          children: [
            { text: '理论学习', level: 2 },
            { text: '实践练习', level: 2 },
            { text: '总结反思', level: 2 }
          ]
        },
        {
          text: '学习资源',
          level: 1,
          children: [
            { text: '书籍资料', level: 2 },
            { text: '在线课程', level: 2 }
          ]
        }
      ]
    } else {
      // 默认通用模拟数据
      mockNodes = [
        {
          text: '主要方面',
          level: 1,
          children: [
            { text: '核心要点1', level: 2 },
            { text: '核心要点2', level: 2 },
            { text: '核心要点3', level: 2 }
          ]
        },
        {
          text: '相关因素',
          level: 1,
          children: [
            { text: '影响因素1', level: 2 },
            { text: '影响因素2', level: 2 }
          ]
        },
        {
          text: '实施建议',
          level: 1,
          children: [
            { text: '具体措施1', level: 2 },
            { text: '具体措施2', level: 2 }
          ]
        }
      ]
    }

    return {
      success: true,
      data: {
        nodes: mockNodes
      },
      message: 'AI生成成功（模拟数据）'
    }
  }
}

// 导出默认实例
export const aiService = new AIService()

// 导出工具函数
export const generateMindMapFromAI = async (content: string): Promise<AIGenerateResponse> => {
  return aiService.generateMindMap({
    content,
    type: 'mindmap',
    options: {
      maxNodes: 10,
      depth: 2,
      language: 'zh-CN'
    }
  })
}
