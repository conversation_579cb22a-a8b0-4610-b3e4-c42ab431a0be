# 思维导图AI功能说明

## 功能概述

为思维导图添加了AI创作功能，用户可以通过右键菜单调用AI来自动生成思维导图内容。

## 功能特点

### 1. 使用限制
- **仅根节点可用**：AI功能只在根节点的右键菜单中启用
- **智能禁用**：非根节点的AI菜单项会显示为禁用状态

### 2. 输入内容
- **默认传递**：AI创作会自动传递当前根节点的文本内容作为输入
- **智能分析**：AI会根据输入内容的类型生成相关的思维导图结构

### 3. 生成结果
- **多层级结构**：自动生成1-2级的思维导图节点
- **智能分类**：根据输入内容生成相关的主题分支
- **自动布局**：生成后自动重新计算节点布局

## 使用方法

1. **右键点击根节点**
2. **选择"AI创作"菜单项**
3. **等待AI生成结果**
4. **查看自动添加的思维导图内容**

## 技术实现

### 1. 组件修改

#### MindMapNodeWithContextMenu.tsx
- 添加了AI图标组件
- 新增AI创作菜单项
- 实现根节点限制逻辑
- 添加菜单分隔符

#### MindMapCanvas.tsx
- 添加onAIGenerate属性支持
- 传递AI处理函数到子组件

#### MindMapPage.tsx
- 实现handleAIGenerate处理函数
- 集成AI服务调用
- 处理生成结果并添加到思维导图

### 2. AI服务

#### aiService.ts
- **AIService类**：封装AI服务调用逻辑
- **接口定义**：完整的请求/响应类型定义
- **模拟数据**：提供开发测试用的模拟响应
- **错误处理**：完善的错误处理和用户提示

### 3. 后端接口预留

```typescript
// 预留的后端接口调用代码（已注释）
const response = await fetch(`${this.baseURL}/api/ai/generate-mindmap`, {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify(request),
})
```

## 后端集成指南

### 1. Koa框架实现

当后端Koa框架实现完成后，需要：

1. **取消注释**：在`aiService.ts`中取消注释真实的API调用代码
2. **注释模拟**：注释掉`getMockResponse`的调用
3. **配置URL**：根据实际部署情况修改`baseURL`

### 2. API接口规范

#### 请求格式
```typescript
POST /api/ai/generate-mindmap
Content-Type: application/json

{
  "content": "项目管理",
  "type": "mindmap",
  "options": {
    "maxNodes": 10,
    "depth": 2,
    "language": "zh-CN"
  }
}
```

#### 响应格式
```typescript
{
  "success": true,
  "data": {
    "nodes": [
      {
        "text": "项目规划",
        "level": 1,
        "children": [
          { "text": "需求分析", "level": 2 },
          { "text": "技术选型", "level": 2 }
        ]
      }
    ]
  },
  "message": "生成成功"
}
```

## 样式定制

### CSS样式
- AI图标使用紫色主题色 (`#6366f1`)
- 悬停时颜色加深 (`#4f46e5`)
- 与现有右键菜单样式保持一致

### 图标设计
- 使用星形图标表示AI功能
- SVG格式，支持颜色自定义
- 16x16像素尺寸

## 测试建议

### 1. 功能测试
- 测试根节点右键菜单AI功能可用
- 测试非根节点AI功能禁用
- 测试不同输入内容的生成效果

### 2. 错误处理测试
- 测试网络错误情况
- 测试后端服务不可用情况
- 测试无效响应数据处理

### 3. 性能测试
- 测试大量节点生成的性能
- 测试布局重计算的效率
- 测试内存使用情况

## 未来扩展

### 1. 功能增强
- 支持自定义生成参数
- 支持多种生成模式
- 支持生成结果预览

### 2. 用户体验
- 添加生成进度提示
- 支持生成结果编辑
- 添加生成历史记录

### 3. AI能力
- 支持更多语言
- 支持图片输入
- 支持语音输入

## 注意事项

1. **性能考虑**：大量节点生成时使用setTimeout分批处理
2. **用户反馈**：提供清晰的成功/失败提示
3. **错误恢复**：网络错误时提供重试机制
4. **数据安全**：确保用户输入数据的隐私保护
