/* eslint-disable @typescript-eslint/no-unused-vars */
import {
  FontBoldIcon,
  FontItalicIcon,
  UnderlineIcon,
} from "@radix-ui/react-icons"
import * as Separator from "@radix-ui/react-separator"
import * as Toggle from "@radix-ui/react-toggle"
import * as Toolbar from "@radix-ui/react-toolbar"
import * as Tooltip from "@radix-ui/react-tooltip"
import type { NodeStyle } from "../types/mindmap"
import { ColorPicker } from "./ColorPicker"

interface StylePanelProps {
  nodeId: string
  nodeX: number
  nodeY: number
  style: NodeStyle
  onStyleChange: (style: NodeStyle) => void
}

export const StylePanel = ({
  nodeId: _nodeId,
  nodeX,
  nodeY,
  style,
  onStyleChange,
}: StylePanelProps) => {
  const updateStyle = (updates: Partial<NodeStyle>) => {
    const newStyle = { ...style, ...updates }
    onStyleChange(newStyle)
  }

  return (
    <Tooltip.Provider>
      <div
        className="style-panel"
        style={{
          left: nodeX - 100,
          top: nodeY + 40,
          zIndex: 1001,
        }}
        onClick={(e) => e.stopPropagation()}
      >
        <div className="style-panel-content">
          <Toolbar.Root className="style-toolbar">
            {/* 粗体按钮 */}
            <Tooltip.Root>
              <Tooltip.Trigger asChild>
                <Toggle.Root
                  className={`style-btn ${
                    style.fontWeight === "bold" ? "active" : ""
                  }`}
                  pressed={style.fontWeight === "bold"}
                  onPressedChange={(pressed) => {
                    const newWeight: "normal" | "bold" = pressed ? "bold" : "normal"
                    console.log("点击粗体按钮，新权重:", newWeight)
                    updateStyle({ fontWeight: newWeight })
                  }}
                >
                  <FontBoldIcon />
                </Toggle.Root>
              </Tooltip.Trigger>
              <Tooltip.Portal>
                <Tooltip.Content className="tooltip-content" sideOffset={5}>
                  粗体
                  <Tooltip.Arrow className="tooltip-arrow" />
                </Tooltip.Content>
              </Tooltip.Portal>
            </Tooltip.Root>

            {/* 斜体按钮 */}
            <Tooltip.Root>
              <Tooltip.Trigger asChild>
                <Toggle.Root
                  className={`style-btn ${
                    style.fontStyle === "italic" ? "active" : ""
                  }`}
                  pressed={style.fontStyle === "italic"}
                  onPressedChange={(pressed) => {
                    updateStyle({
                      fontStyle: pressed ? "italic" : "normal",
                    })
                  }}
                >
                  <FontItalicIcon />
                </Toggle.Root>
              </Tooltip.Trigger>
              <Tooltip.Portal>
                <Tooltip.Content className="tooltip-content" sideOffset={5}>
                  斜体
                  <Tooltip.Arrow className="tooltip-arrow" />
                </Tooltip.Content>
              </Tooltip.Portal>
            </Tooltip.Root>

            {/* 下划线按钮 */}
            <Tooltip.Root>
              <Tooltip.Trigger asChild>
                <Toggle.Root
                  className={`style-btn ${
                    style.textDecoration === "underline" ? "active" : ""
                  }`}
                  pressed={style.textDecoration === "underline"}
                  onPressedChange={(pressed) => {
                    updateStyle({
                      textDecoration: pressed ? "underline" : "none",
                    })
                  }}
                >
                  <UnderlineIcon />
                </Toggle.Root>
              </Tooltip.Trigger>
              <Tooltip.Portal>
                <Tooltip.Content className="tooltip-content" sideOffset={5}>
                  下划线
                  <Tooltip.Arrow className="tooltip-arrow" />
                </Tooltip.Content>
              </Tooltip.Portal>
            </Tooltip.Root>

            {/* 分隔线 */}
            <Separator.Root
              className="separator-vertical"
              orientation="vertical"
              style={{
                width: "1px",
                height: "20px",
                backgroundColor: "#e5e7eb",
                margin: "0 8px",
              }}
            />

            {/* 字体大小 */}
            <Tooltip.Root>
              <Tooltip.Trigger asChild>
                <input
                  type="number"
                  min="10"
                  max="24"
                  value={style.fontSize}
                  onChange={(e) => {
                    updateStyle({ fontSize: parseInt(e.target.value) || 14 })
                  }}
                  className="style-input"
                  style={{
                    width: "50px",
                    height: "26px",
                    fontSize: "12px",
                  }}
                />
              </Tooltip.Trigger>
              <Tooltip.Portal>
                <Tooltip.Content className="tooltip-content" sideOffset={5}>
                  字体大小
                  <Tooltip.Arrow className="tooltip-arrow" />
                </Tooltip.Content>
              </Tooltip.Portal>
            </Tooltip.Root>

            {/* 分隔线 */}
            <Separator.Root
              className="separator-vertical"
              orientation="vertical"
              style={{
                width: "1px",
                height: "20px",
                backgroundColor: "#e5e7eb",
                margin: "0 8px",
              }}
            />

            {/* 文字颜色 */}
            <ColorPicker
              value={style.color}
              onChange={(color: string) => updateStyle({ color })}
              title="文字颜色"
              icon="A"
            />

            {/* 背景颜色 */}
            <ColorPicker
              value={style.backgroundColor}
              onChange={(color: string) =>
                updateStyle({ backgroundColor: color })
              }
              title="背景颜色"
              icon="■"
            />
          </Toolbar.Root>
        </div>
      </div>
    </Tooltip.Provider>
  )
}
