import { useState } from 'react'
import type { ContextMenuState } from '../types/mindmap'

export const useContextMenu = () => {
  const [contextMenu, setContextMenu] = useState<ContextMenuState>({
    visible: false,
    x: 0,
    y: 0,
    nodeId: null,
  })

  // 设置当前右键菜单的节点ID（用于 Radix UI ContextMenu）
  const setContextMenuNodeId = (nodeId: string | null) => {
    setContextMenu(prev => ({
      ...prev,
      nodeId: nodeId,
    }))
  }

  // 显示右键菜单（保留用于兼容性，但 Radix UI 会自动处理显示）
  const showContextMenu = (e: React.MouseEvent, nodeId: string) => {
    e.preventDefault()
    e.stopPropagation()
    setContextMenu({
      visible: true,
      x: e.clientX,
      y: e.clientY,
      nodeId: nodeId,
    })
  }

  // 隐藏右键菜单
  const hideContextMenu = () => {
    setContextMenu({
      visible: false,
      x: 0,
      y: 0,
      nodeId: null,
    })
  }

  return {
    contextMenu,
    setContextMenuNodeId,
    showContextMenu,
    hideContextMenu,
  }
}
