# 思维导图重置功能说明

## 功能概述

我已经为您添加了一个重置思维导图的功能，可以清除localStorage中存储的所有思维导图数据并重置为默认状态。

## 使用方法

### 方法1：使用Header中的重置按钮

1. 在页面顶部的Header区域，您会看到一个新的"重置"按钮（带有勾选图标）
2. 点击该按钮
3. 系统会弹出确认对话框："确定要重置思维导图吗？这将清除所有数据并无法恢复。"
4. 点击"确定"即可重置思维导图

### 方法2：在浏览器控制台中手动调用

如果您需要在开发过程中快速重置，也可以在浏览器控制台中执行：

```javascript
// 方法1：直接清除localStorage
localStorage.removeItem('mindmap-nodes')
location.reload()

// 方法2：通过Redux store调用重置action
// (需要在有Redux context的页面中执行)
window.__REDUX_DEVTOOLS_EXTENSION__ && store.dispatch({type: 'mindMap/resetMindMap'})
```

## 技术实现

### 1. Redux Action
使用了现有的 `resetMindMap` action：

```typescript
resetMindMap: (state) => {
  const defaultNodes = {
    root: {
      id: "root",
      text: "未命名文件(1)",
      x: 500,
      y: 300,
      level: 0,
      children: [],
      style: getDefaultNodeStyle(),
    },
  }
  state.nodes = defaultNodes
  state.selectedNodeId = null
  // 清除localStorage
  clearMindMapStorage()
}
```

### 2. Header组件集成
- 在 `hooks.ts` 中添加了 `handleReset` 函数
- 在 `constants.ts` 中添加了重置按钮配置
- 在 `FileControls.tsx` 中集成了重置按钮

### 3. 安全确认
- 添加了确认对话框，防止误操作
- 明确提示数据将无法恢复

## 重置后的状态

重置后，思维导图将恢复到初始状态：
- 只有一个根节点："未命名文件(1)"
- 位置在画布中心 (500, 300)
- 清除所有子节点
- 清除localStorage中的数据
- 重置选中状态

## 注意事项

1. **数据不可恢复**：重置操作会永久删除所有思维导图数据，请谨慎使用
2. **确认对话框**：每次重置都会弹出确认对话框，确保不会误操作
3. **立即生效**：重置后立即生效，无需刷新页面
4. **localStorage清除**：会同时清除浏览器localStorage中的数据

## 开发用途

这个功能特别适用于：
- 开发和测试过程中快速清除测试数据
- 演示时重置到干净状态
- 解决localStorage数据损坏问题
- 快速开始新的思维导图创作

## 故障排除

如果重置按钮不工作，请检查：

1. **Redux连接**：确保组件正确连接到Redux store
2. **控制台错误**：查看浏览器控制台是否有错误信息
3. **手动清除**：可以使用浏览器控制台手动清除localStorage

```javascript
// 手动清除localStorage
localStorage.clear()
location.reload()
```

## 扩展功能

未来可以考虑添加：
- 重置前的数据备份
- 选择性重置（只重置某些部分）
- 重置历史记录
- 导出数据后再重置

现在您可以使用Header中的重置按钮来清除localStorage中的思维导图数据了！
