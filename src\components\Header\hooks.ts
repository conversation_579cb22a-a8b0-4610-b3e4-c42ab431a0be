import { useCallback, useState } from "react"
import { useAppDispatch } from "../../store/hooks"
import { resetMindMap } from "../../store/mindMapSlice"

// 文件状态管理 Hook
export const useFileState = () => {
  const [fileName, setFileName] = useState("未命名文件")
  const [isStarred, setIsStarred] = useState(false)
  const [location, setLocation] = useState("金山办公软件有限公司 > 我的云文档")

  const toggleStar = useCallback(() => {
    setIsStarred(prev => !prev)
  }, [])

  return {
    fileName,
    setFileName,
    isStarred,
    toggleStar,
    location,
    setLocation,
  }
}

// 文件操作 Hook
export const useFileActions = () => {
  const dispatch = useAppDispatch()
  const handleHome = useCallback(() => {
    console.log("Navigate to home")
    // TODO: 实际的导航逻辑
  }, [])

  const handleNew = useCallback(() => {
    console.log("Create new file")
    // TODO: 实际的新建文件逻辑
  }, [])

  const handleReset = useCallback(() => {
    if (confirm("确定要重置思维导图吗？这将清除所有数据并无法恢复。")) {
      dispatch(resetMindMap())
      console.log("思维导图已重置")
    }
  }, [dispatch])

  const handleMenu = useCallback(() => {
    console.log("Open document menu")
    // TODO: 实际的菜单逻辑
  }, [])

  const handleShare = useCallback(() => {
    console.log("Share file")
    // TODO: 实际的分享逻辑
  }, [])

  const handleTag = useCallback(() => {
    console.log("Add tag to file")
    // TODO: 实际的标签逻辑
  }, [])

  const handleMove = useCallback(() => {
    console.log("Move file")
    // TODO: 实际的移动文件逻辑
  }, [])

  const handleAddShortcut = useCallback(() => {
    console.log("Add shortcut")
    // TODO: 实际的添加快捷方式逻辑
  }, [])

  return {
    handleHome,
    handleNew,
    handleReset,
    handleMenu,
    handleShare,
    handleTag,
    handleMove,
    handleAddShortcut,
  }
}

// 用户操作 Hook
export const useUserActions = () => {
  const handleShareClick = useCallback(() => {
    console.log("User share clicked")
    // TODO: 实际的用户分享逻辑
  }, [])

  const handleUserClick = useCallback(() => {
    console.log("User avatar clicked")
    // TODO: 实际的用户菜单逻辑
  }, [])

  return {
    handleShareClick,
    handleUserClick,
  }
}
