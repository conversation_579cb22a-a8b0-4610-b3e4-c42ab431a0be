import { ChevronDownIcon } from "@radix-ui/react-icons"
import * as Popover from "@radix-ui/react-popover"
import React from "react"
import { IconButton } from "../common/IconButton"
import {
    createFileActions,
    createFileOperations,
    createLocationOperations,
} from "./constants"
import { useFileActions, useFileState } from "./hooks"
import { getIconComponent } from "./iconUtils"
import type { FileControlsProps } from "./types"

export const FileControls: React.FC<FileControlsProps> = ({
  fileName: propFileName,
  onFileNameChange,
}) => {
  const { fileName, toggleStar, location } = useFileState()
  const {
    handleHome,
    handleNew,
    handleReset,
    handleMenu,
    handleShare,
    handleTag,
    handleMove,
    handleAddShortcut,
  } = useFileActions()

  // 使用传入的文件名或默认文件名
  const displayFileName = propFileName || fileName

  // 创建配置
  const fileActions = createFileActions({
    handleHome,
    handleNew,
    handleReset,
    handleMenu,
    toggleStar,
  })

  const fileOperations = createFileOperations({
    handleShare,
    toggleStar,
    handleTag,
  })

  const locationOperations = createLocationOperations({
    handleMove,
    handleAddShortcut,
  })

  return (
    <div className="file-controls">
      {/* 基础文件操作按钮 */}
      {fileActions.map((action) => (
        <IconButton
          key={action.id}
          icon={action.icon}
          label={action.label}
          onClick={action.onClick}
          disabled={action.disabled}
        />
      ))}

      <span className="file-name">{displayFileName}</span>

      {/* 文件操作弹出菜单 */}
      <Popover.Root>
        <Popover.Trigger asChild>
          <IconButton
            icon={ChevronDownIcon}
            label="更多选项"
            className="file-popover-trigger"
          />
        </Popover.Trigger>
        <Popover.Portal>
          <Popover.Content
            className="popover-content"
            side="bottom"
            align="start"
            sideOffset={4}
          >
            <div className="card-panel">
              {/* 顶部卡片 - 文件信息和操作 */}
              <div className="card-top">
                <div className="card-top-file">
                  <div className="file-icon">📄</div>
                  <div className="file-info">
                    <div className="file-name">{displayFileName}</div>
                    <div className="file-actions">
                      {fileOperations.map((operation, index) => (
                        <React.Fragment key={operation.id}>
                          <button
                            className="action-btn"
                            onClick={operation.onClick}
                          >
                            {React.createElement(
                              getIconComponent(operation.icon),
                              {
                                className: "action-icon",
                              }
                            )}
                            {operation.label}
                          </button>
                          {index < fileOperations.length - 1 && (
                            <div className="separator" />
                          )}
                        </React.Fragment>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              {/* 底部卡片 - 位置信息和操作 */}
              <div className="card-bottom">
                <div className="location-info">
                  <div className="folder-icon">📁</div>
                  <div className="location-path">{location}</div>
                </div>
                <div className="location-actions">
                  {locationOperations.map((operation) => (
                    <button
                      key={operation.id}
                      className="location-btn"
                      onClick={operation.onClick}
                    >
                      {React.createElement(getIconComponent(operation.icon), {
                        className: "location-icon",
                      })}
                      {operation.label}
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </Popover.Content>
        </Popover.Portal>
      </Popover.Root>
    </div>
  )
}
