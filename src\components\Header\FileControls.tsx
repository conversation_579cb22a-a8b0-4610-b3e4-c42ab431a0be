/* eslint-disable @typescript-eslint/no-unused-vars */
import { ChevronDownIcon } from "@radix-ui/react-icons"
import * as Popover from "@radix-ui/react-popover"
import * as Tooltip from "@radix-ui/react-tooltip"
import React from "react"
import { IconButton } from "../common/IconButton"
import {
  createFileActions,
  createFileOperations,
  createLocationOperations,
} from "./constants"
import { useFileActions, useFileState } from "./hooks"
import type { FileControlsProps } from "./types"

// 专门用于 Popover 的图标按钮组件，支持 Tooltip
const PopoverIconButton = React.forwardRef<
  HTMLButtonElement,
  {
    icon: React.ComponentType<{ className?: string }>
    label: string
    onClick?: () => void
    className?: string
  }
>(({ icon: Icon, label, onClick, className = "" }, ref) => {
  const [showTooltip, setShowTooltip] = React.useState(false)

  return (
    <Tooltip.Root open={showTooltip} onOpenChange={setShowTooltip}>
      <Tooltip.Trigger asChild>
        <button
          ref={ref}
          className={`icon-btn icon-btn-md ${className}`}
          onClick={onClick}
          aria-label={label}
          type="button"
          onMouseEnter={() => setShowTooltip(true)}
          onMouseLeave={() => setShowTooltip(false)}
          onFocus={() => setShowTooltip(true)}
          onBlur={() => setShowTooltip(false)}
        >
          <Icon />
        </button>
      </Tooltip.Trigger>
      <Tooltip.Portal>
        <Tooltip.Content className="tooltip-content" side="bottom" align="center" sideOffset={5}>
          {label}
          <Tooltip.Arrow className="tooltip-arrow" />
        </Tooltip.Content>
      </Tooltip.Portal>
    </Tooltip.Root>
  )
})

export const FileControls: React.FC<FileControlsProps> = ({
  fileName: propFileName,
  // onFileNameChange, // 暂时未使用
}) => {
  const [popoverOpen, setPopoverOpen] = React.useState(false)
  const { fileName, toggleStar, location } = useFileState()
  const {
    handleHome,
    handleNew,
    handleReset,
    handleMenu,
    handleShare,
    handleTag,
    handleMove,
    handleAddShortcut,
  } = useFileActions()

  // 使用传入的文件名或默认文件名
  const displayFileName = propFileName || fileName

  // 创建配置
  const fileActions = createFileActions({
    handleHome,
    handleNew,
    handleReset,
    handleMenu,
    toggleStar,
  })

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const fileOperations = createFileOperations({
    handleShare,
    toggleStar,
    handleTag,
  })

  const locationOperations = createLocationOperations({
    handleMove,
    handleAddShortcut,
  })

  return (
    <div className="file-controls">
      {/* 基础文件操作按钮 */}
      {fileActions.map((action) => (
        <IconButton
          key={action.id}
          icon={action.icon as React.ComponentType<{ className?: string }>}
          label={action.label}
          onClick={action.onClick}
          disabled={action.disabled}
        />
      ))}

      <span className="file-name">{displayFileName}</span>

      {/* 文件操作弹出菜单 */}
      <Tooltip.Root>
        <Tooltip.Trigger asChild>
          <Popover.Root
            open={popoverOpen}
            onOpenChange={(open) => {
              console.log("Popover open state:", open)
              setPopoverOpen(open)
            }}
          >
            <Popover.Trigger asChild>
              <button
                className="icon-btn icon-btn-md file-popover-trigger"
                onClick={() => {
                  console.log("Button clicked")
                  setPopoverOpen(!popoverOpen)
                }}
                aria-label="更多选项"
                type="button"
              >
                <ChevronDownIcon />
              </button>
            </Popover.Trigger>
        <Popover.Content
          side="bottom"
          align="start"
          sideOffset={4}
          style={{
            zIndex: 9999,
            backgroundColor: 'white',
            border: '1px solid #e2e8f0',
            borderRadius: '8px',
            padding: '16px',
            boxShadow: '0 8px 24px rgba(0, 0, 0, 0.1)',
            width: '260px',
            maxWidth: '90vw'
          }}
        >
          <div>
            <h3 style={{ margin: '0 0 8px 0', fontSize: '14px', fontWeight: 'bold' }}>测试 Popover</h3>
            <p style={{ margin: '4px 0', fontSize: '12px' }}>文件名: {displayFileName}</p>
            <p style={{ margin: '4px 0', fontSize: '12px' }}>位置: {location}</p>
            <button
              onClick={() => console.log("测试按钮点击")}
              style={{
                padding: '4px 8px',
                backgroundColor: '#f0f0f0',
                border: '1px solid #ccc',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              测试按钮
            </button>
          </div>
        </Popover.Content>
      </Popover.Root>
        </Tooltip.Trigger>
        <Tooltip.Portal>
          <Tooltip.Content className="tooltip-content" side="bottom" align="center" sideOffset={5}>
            更多选项
            <Tooltip.Arrow className="tooltip-arrow" />
          </Tooltip.Content>
        </Tooltip.Portal>
      </Tooltip.Root>
    </div>
  )
}
