import { ChevronDownIcon } from "@radix-ui/react-icons"
import * as Popover from "@radix-ui/react-popover"
import React from "react"
import { IconButton } from "../common/IconButton"
import {
  createFileActions,
  createFileOperations,
  createLocationOperations,
} from "./constants"
import { useFileActions, useFileState } from "./hooks"
import type { FileControlsProps } from "./types"

export const FileControls: React.FC<FileControlsProps> = ({
  fileName: propFileName,
  // onFileNameChange, // 暂时未使用
}) => {
  const { fileName, toggleStar, location } = useFileState()
  const {
    handleHome,
    handleNew,
    handleReset,
    handleMenu,
    handleShare,
    handleTag,
    handleMove,
    handleAddShortcut,
  } = useFileActions()

  // 使用传入的文件名或默认文件名
  const displayFileName = propFileName || fileName

  // 创建配置
  const fileActions = createFileActions({
    handleHome,
    handleNew,
    handleReset,
    handleMenu,
    toggleStar,
  })

  const fileOperations = createFileOperations({
    handleShare,
    toggleStar,
    handleTag,
  })

  const locationOperations = createLocationOperations({
    handleMove,
    handleAddShortcut,
  })

  return (
    <div className="file-controls">
      {/* 基础文件操作按钮 */}
      {fileActions.map((action) => (
        <IconButton
          key={action.id}
          icon={action.icon as React.ComponentType<{ className?: string }>}
          label={action.label}
          onClick={action.onClick}
          disabled={action.disabled}
        />
      ))}

      <span className="file-name">{displayFileName}</span>

      {/* 文件操作弹出菜单 */}
      <Popover.Root onOpenChange={(open) => console.log("Popover open state:", open)}>
        <Popover.Trigger asChild>
          <IconButton
            icon={ChevronDownIcon}
            label="更多选项"
            className="file-popover-trigger"
            onClick={() => console.log("IconButton clicked")}
          />
        </Popover.Trigger>
        <Popover.Portal>
          <Popover.Content
            className="popover-content"
            side="bottom"
            align="start"
            sideOffset={4}
            style={{ zIndex: 9999 }}
          >
            <div className="card-panel">
              <div style={{ padding: "16px", background: "white", borderRadius: "8px" }}>
                <h3>测试 Popover</h3>
                <p>文件名: {displayFileName}</p>
                <p>位置: {location}</p>
                <button onClick={() => console.log("测试按钮点击")}>测试按钮</button>
              </div>
            </div>
          </Popover.Content>
        </Popover.Portal>
      </Popover.Root>
    </div>
  )
}
