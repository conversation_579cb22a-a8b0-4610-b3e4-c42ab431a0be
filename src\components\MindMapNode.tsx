import { PlusIcon } from "@radix-ui/react-icons"
import React, { useCallback, useEffect, useRef } from "react"
import type {
  MindMapNode as MindMapNodeType,
  NodeStyle,
} from "../types/mindmap"
import { StylePanel } from "./StylePanel"

interface MindMapNodeProps {
  node: MindMapNodeType
  rootNode: MindMapNodeType
  isSelected: boolean
  isEditing: boolean
  editingText: string
  editingStyle: NodeStyle
  showStylePanel: boolean
  showAddButton: boolean
  isDragging: boolean
  onSelect: () => void
  onDoubleClick: (e?: React.MouseEvent) => void
  onContextMenu: (e: React.MouseEvent) => void
  onMouseDown: (e: React.MouseEvent) => void
  onEditingTextChange: (text: string) => void
  onSaveEdit: () => void
  onCancelEdit: () => void
  onStyleChange: (style: NodeStyle) => void
  onAddChild: () => void
  onToggleBold?: () => void
  onToggleItalic?: () => void
  onToggleUnderline?: () => void
  onColorChange?: (color: string) => void
  onFontSizeChange?: (fontSize: number) => void
  onTextAlignChange?: (align: "left" | "center" | "right") => void
}

export const MindMapNode = ({
  node,
  rootNode,
  isSelected,
  isEditing,
  editingText,
  editingStyle,
  showStylePanel,
  showAddButton,
  // isDragging,
  onSelect,
  onDoubleClick,
  onContextMenu,
  onMouseDown,
  onEditingTextChange,
  onSaveEdit,
  onCancelEdit,
  onStyleChange,
  onAddChild,
}: MindMapNodeProps) => {
  const nodeRef = useRef<HTMLDivElement>(null)
  const editableRef = useRef<HTMLSpanElement>(null)



  // 计算加号按钮位置
  const getAddButtonPosition = () => {
    if (!nodeRef.current) return { left: node.x + 100, top: node.y + 4 }

    const nodeRect = nodeRef.current.getBoundingClientRect()
    const nodeWidth = nodeRect.width

    let addButtonLeft: number

    if (node.id === "root") {
      // 根节点：加号紧靠节点右边缘
      addButtonLeft = node.x + nodeWidth / 2
    } else if (node.x > rootNode.x) {
      // 节点在根节点右侧：加号紧靠节点右边缘
      addButtonLeft = node.x + nodeWidth / 2
    } else {
      // 节点在根节点左侧：加号紧靠节点左边缘
      addButtonLeft = node.x - nodeWidth / 2 - 24 // 加号宽度24px，紧靠左边缘
    }

    return {
      left: addButtonLeft,
      top: node.y + 4, // 与节点垂直居中对齐
    }
  }

  // 处理可编辑span的键盘事件
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      onSaveEdit()
    }
    if (e.key === "Escape") {
      e.preventDefault()
      onCancelEdit()
    }
  }

  // 保存和恢复光标位置的辅助函数
  const saveCursorPosition = useCallback((element: HTMLElement) => {
    const selection = window.getSelection()
    if (selection && selection.rangeCount > 0) {
      const range = selection.getRangeAt(0)
      const preCaretRange = range.cloneRange()
      preCaretRange.selectNodeContents(element)
      preCaretRange.setEnd(range.endContainer, range.endOffset)
      return preCaretRange.toString().length
    }
    return 0
  }, [])

  const restoreCursorPosition = useCallback(
    (element: HTMLElement, position: number) => {
      const selection = window.getSelection()
      if (!selection) return

      let charIndex = 0
      const walker = document.createTreeWalker(element, NodeFilter.SHOW_TEXT)

      let node
      while ((node = walker.nextNode())) {
        const textNode = node as Text
        const nextCharIndex = charIndex + textNode.textContent!.length

        if (position <= nextCharIndex) {
          const range = document.createRange()
          range.setStart(textNode, position - charIndex)
          range.collapse(true)
          selection.removeAllRanges()
          selection.addRange(range)
          return
        }
        charIndex = nextCharIndex
      }

      // 如果位置超出范围，将光标放在末尾
      const range = document.createRange()
      range.selectNodeContents(element)
      range.collapse(false)
      selection.removeAllRanges()
      selection.addRange(range)
    },
    []
  )



  // 处理文本变化
  const handleInput = (e: React.FormEvent<HTMLSpanElement>) => {
    const text = e.currentTarget.textContent || ""
    const cursorPosition = saveCursorPosition(e.currentTarget)

    // 限制文本长度为100个字符
    if (text.length <= 100) {
      onEditingTextChange(text)
    } else {
      // 如果超过100个字符，截断文本并更新显示
      const truncatedText = text.substring(0, 100)
      e.currentTarget.textContent = truncatedText
      onEditingTextChange(truncatedText)

      // 恢复光标位置（调整到截断后的位置）
      setTimeout(() => {
        restoreCursorPosition(e.currentTarget, Math.min(cursorPosition, 100))
      }, 0)
    }
  }

  // 当进入编辑模式时，聚焦并选中文本
  useEffect(() => {
    if (isEditing && editableRef.current) {
      // 设置初始文本内容
      editableRef.current.textContent = editingText
      editableRef.current.focus()

      // 选中所有文本
      const range = document.createRange()
      range.selectNodeContents(editableRef.current)
      const selection = window.getSelection()
      if (selection) {
        selection.removeAllRanges()
        selection.addRange(range)
      }
    }
  }, [isEditing])

  // 当编辑文本从外部更新时，同步到DOM（但保持光标位置）
  useEffect(() => {
    if (
      isEditing &&
      editableRef.current &&
      editableRef.current.textContent !== editingText
    ) {
      const cursorPosition = saveCursorPosition(editableRef.current)
      editableRef.current.textContent = editingText
      setTimeout(() => {
        restoreCursorPosition(editableRef.current!, cursorPosition)
      }, 0)
    }
  }, [editingText, isEditing])

  return (
    <div key={node.id}>
      <div
        ref={nodeRef}
        data-node-id={node.id}
        className={`mindmap-node level-${node.level} ${
          isSelected ? "selected" : ""
        }`}
        style={{
          left: node.x,
          top: node.y,
          fontSize: `${
            showStylePanel ? editingStyle.fontSize : node.style?.fontSize || 14
          }px`,
          fontFamily: showStylePanel
            ? editingStyle.fontFamily
            : node.style?.fontFamily || "微软雅黑",
          fontWeight: showStylePanel
            ? editingStyle.fontWeight
            : node.style?.fontWeight || "normal",
          fontStyle: showStylePanel
            ? editingStyle.fontStyle
            : node.style?.fontStyle || "normal",
          textDecoration: showStylePanel
            ? editingStyle.textDecoration
            : node.style?.textDecoration || "none",
          color: showStylePanel
            ? editingStyle.color
            : node.style?.color || "#000000",
          backgroundColor: showStylePanel
            ? editingStyle.backgroundColor
            : node.style?.backgroundColor || "#ffffff",
          borderColor: showStylePanel
            ? editingStyle.borderColor
            : node.style?.borderColor || "#d1d5db",
          borderWidth: `${
            showStylePanel
              ? editingStyle.borderWidth
              : node.style?.borderWidth || 1
          }px`,
          borderStyle: "solid",
          textAlign: showStylePanel
            ? editingStyle.textAlign || "center"
            : node.style?.textAlign || "center",
          cursor: "default",
          width: "max-content", // 让宽度完全由内容决定
          minWidth:
            node.level === 0 ? "100px" : node.level === 1 ? "80px" : "70px",
          // 设置更大的最大宽度，确保内容不会溢出
          maxWidth: node.level === 0 ? "800px" : "600px",
          whiteSpace: "pre-wrap",
          wordWrap: "break-word",
          wordBreak: "break-all", // 更激进的换行策略，在任何字符处换行
          overflowWrap: "anywhere", // 最新的换行属性，允许在任何地方换行
          hyphens: "auto", // 启用连字符换行
          overflow: "visible",
          height: "auto",
          minHeight:
            node.level === 0 ? "40px" : node.level === 1 ? "36px" : "32px",
        }}
        onClick={onSelect}
        onDoubleClick={onDoubleClick}
        onContextMenu={onContextMenu}
        onMouseDown={onMouseDown}
      >
        {isEditing ? (
          <div style={{ position: "relative", width: "100%" }}>
            <span
              ref={editableRef}
              contentEditable
              suppressContentEditableWarning
              style={{
                outline: "none",
                display: "inline-block",
                width: "100%",
                minHeight: "inherit",
              }}
              onInput={handleInput}
              onKeyDown={handleKeyDown}
              onBlur={onSaveEdit}
            />
            {/* 移除 {editingText}，内容通过 useEffect 设置 */}
            {/* 字符计数显示 */}
            <div
              style={{
                position: "absolute",
                bottom: "-20px",
                right: "0",
                fontSize: "10px",
                color: editingText.length > 90 ? "#ff6b6b" : "#666",
                background: "rgba(255, 255, 255, 0.9)",
                padding: "2px 4px",
                borderRadius: "3px",
                whiteSpace: "nowrap",
              }}
            >
              {editingText.length}/100
            </div>
          </div>
        ) : (
          <span
            style={{
              width: "100%",
              display: "block",
              whiteSpace: "pre-wrap",
              wordWrap: "break-word",
              wordBreak: "break-all",
              overflowWrap: "anywhere",
              hyphens: "auto",
            }}
          >
            {node.text}
          </span>
        )}
      </div>

      {/* Style Panel */}
      {showStylePanel && (
        <StylePanel
          nodeId={node.id}
          nodeX={node.x}
          nodeY={node.y}
          style={editingStyle}
          onStyleChange={onStyleChange}
        />
      )}

      {/* 加号按钮 - 只有在点击节点且没有子节点时显示 */}
      {showAddButton && node.children.length === 0 && (
        <button
          className="node-add-btn"
          style={getAddButtonPosition()}
          onClick={(e) => {
            e.stopPropagation()
            onAddChild()
          }}
          title="添加子节点"
        >
          <PlusIcon />
        </button>
      )}
    </div>
  )
}
