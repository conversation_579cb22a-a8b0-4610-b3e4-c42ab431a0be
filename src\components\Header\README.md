# Header 组件

这是一个模块化的 Header 组件，采用了良好的 UI/逻辑分离设计，具有高度的可复用性和可维护性。

## 设计原则

### 1. UI 与逻辑分离
- **UI 组件**：专注于渲染和用户交互
- **Hooks**：管理状态和业务逻辑
- **工厂函数**：动态创建配置，支持依赖注入

### 2. 高度可复用
- 组件支持 props 传入自定义处理函数
- 使用 TypeScript 提供完整的类型安全
- 模块化设计，可单独使用各个子组件

### 3. 易于维护
- 清晰的文件结构和职责分离
- 统一的类型定义和接口
- 完整的导出管理

- **FilePopover**: 文件操作弹出菜单
- **FilePopoverContent**: 弹出菜单内容布局
- **FileInfo**: 文件信息显示
- **FileActions**: 文件操作按钮组
- **LocationInfo**: 位置信息显示
- **LocationActions**: 位置操作按钮组

#### 通用组件

- **IconButton**: 通用图标按钮（带 tooltip）
- **ActionButton**: 通用操作按钮
- **TabButton**: 标签按钮
- **ShareButton**: 分享按钮
- **UserAvatar**: 用户头像

### 3. 设计原则

#### 单一职责原则

每个组件只负责一个特定的功能，职责明确。

#### 可复用性

- `IconButton` 可以在整个应用中使用
- `ActionButton` 可以用于各种操作场景
- 图标组件统一管理，便于维护

#### 可配置性

- 通过 `constants.ts` 集中管理配置
- 支持动态配置按钮、标签等

#### 可扩展性

- 新增功能只需添加新组件
- 修改现有功能不影响其他组件
- 支持插件式扩展

### 4. 性能优化

#### 组件拆分

- 避免不必要的重渲染
- 使用 `React.memo` 优化子组件
- 合理使用 `useCallback` 和 `useMemo`

#### 状态管理

- 使用 `useHeader` hook 管理状态
- 避免状态提升过高
- 局部状态局部管理

#### 懒加载

- 弹出菜单内容按需加载
- 图标组件按需导入

### 5. 使用示例

```tsx
import { Header, useHeader } from "./components/Header"

const App = () => {
  const { activeTab, handleTabChange } = useHeader({
    initialActiveTab: "开始",
    onTabChange: (tab) => console.log("Tab changed:", tab),
  })

  return <Header activeTab={activeTab} onTabChange={handleTabChange} />
}
```

### 6. 扩展指南

#### 添加新的文件操作

1. 在 `constants.ts` 中添加配置
2. 在 `icons.tsx` 中添加图标
3. 组件会自动渲染新操作

#### 添加新的标签页

1. 在 `constants.ts` 中修改 `tabConfig`
2. 标签页会自动更新

#### 自定义样式

1. 修改 `Header.css` 中的样式
2. 支持主题定制
3. 响应式设计

### 7. 测试策略

#### 单元测试

- 每个组件独立测试
- 测试 props 传递
- 测试事件处理

#### 集成测试

- 测试组件间交互
- 测试状态管理
- 测试用户操作流程

#### 视觉测试

- 测试不同状态下的显示
- 测试响应式布局
- 测试无障碍访问

## 总结

重构后的 Header 组件具有以下优势：

1. **可维护性**: 组件职责清晰，易于理解和修改
2. **可扩展性**: 支持插件式扩展，新增功能不影响现有代码
3. **可复用性**: 通用组件可在其他地方使用
4. **性能优化**: 合理的组件拆分和状态管理
5. **类型安全**: 完整的 TypeScript 类型定义
6. **测试友好**: 组件独立，易于测试
