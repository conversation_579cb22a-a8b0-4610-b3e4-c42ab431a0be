import type { MindMapNode, NodeStyle } from '../types/mindmap'

// 默认节点样式
export const getDefaultNodeStyle = (): NodeStyle => ({
  fontFamily: "微软雅黑",
  fontSize: 14,
  fontWeight: "normal",
  fontStyle: "normal",
  textDecoration: "none",
  color: "#000000",
  backgroundColor: "#ffffff",
  borderColor: "#d1d5db",
  borderWidth: 1,
})

// 获取节点默认名称
export const getDefaultNodeName = (parentLevel: number) => {
  switch (parentLevel) {
    case 0: // 根节点的子节点
      return "分支主题"
    case 1: // 分支主题的子节点
      return "子主题"
    case 2: // 子主题的子节点
      return "子主题"
    default:
      return "新节点"
  }
}

// 估算节点宽度（更精确的版本）
const estimateNodeWidth = (node: MindMapNode): number => {
  // 尝试从DOM获取实际宽度
  const nodeElement = document.querySelector(`[data-node-id="${node.id}"]`) as HTMLElement
  if (nodeElement && nodeElement.offsetWidth > 0) {
    return nodeElement.offsetWidth
  }

  const fontSize = node.style?.fontSize || 14
  const text = node.text || "新节点"

  // 处理多行文本，计算最宽的一行
  const lines = text.split('\n')
  let maxLineLength = 0
  lines.forEach(line => {
    maxLineLength = Math.max(maxLineLength, line.length)
  })

  // 更精确的字符宽度计算
  const charWidth = fontSize * 0.65 // 稍微增加字符宽度估算
  const textWidth = maxLineLength * charWidth

  // 加上padding和边框
  const padding = node.level === 0 ? 40 : node.level === 1 ? 32 : 24
  const estimatedWidth = textWidth + padding

  // 设置最小和最大宽度，与CSS保持一致
  const minWidth = node.level === 0 ? 120 : node.level === 1 ? 100 : 80
  const maxWidth = node.level === 0 ? 800 : 600 // 与CSS中的maxWidth保持一致

  return Math.min(Math.max(estimatedWidth, minWidth), maxWidth)
}

// 估算节点高度（新增函数）
const estimateNodeHeight = (node: MindMapNode): number => {
  // 尝试从DOM获取实际高度
  const nodeElement = document.querySelector(`[data-node-id="${node.id}"]`) as HTMLElement
  if (nodeElement && nodeElement.offsetHeight > 0) {
    return nodeElement.offsetHeight
  }

  const fontSize = node.style?.fontSize || 14
  const text = node.text || "新节点"

  // 计算行数
  const lines = text.split('\n')
  const lineCount = lines.length

  // 行高约为字体大小的1.2倍
  const lineHeight = fontSize * 1.2
  const textHeight = lineCount * lineHeight

  // 加上padding
  const padding = node.level === 0 ? 24 : node.level === 1 ? 20 : 16
  const estimatedHeight = textHeight + padding

  // 设置最小高度
  const minHeight = node.level === 0 ? 40 : node.level === 1 ? 36 : 32

  return Math.max(estimatedHeight, minHeight)
}

// 检查两个节点是否重叠
// const checkNodeOverlap = (node1: MindMapNode, node2: MindMapNode, margin: number = 20): boolean => {
//   const width1 = estimateNodeWidth(node1)
//   const height1 = estimateNodeHeight(node1)
//   const width2 = estimateNodeWidth(node2)
//   const height2 = estimateNodeHeight(node2)

//   // 计算节点的边界（考虑transform: translate(-50%, -50%)）
//   const left1 = node1.x - width1 / 2 - margin
//   const right1 = node1.x + width1 / 2 + margin
//   const top1 = node1.y - height1 / 2 - margin
//   const bottom1 = node1.y + height1 / 2 + margin

//   const left2 = node2.x - width2 / 2 - margin
//   const right2 = node2.x + width2 / 2 + margin
//   const top2 = node2.y - height2 / 2 - margin
//   const bottom2 = node2.y + height2 / 2 + margin

//   // 检查是否重叠
//   return !(right1 < left2 || left1 > right2 || bottom1 < top2 || top1 > bottom2)
// }

// // 计算动态垂直间距
// const calculateDynamicSpacing = (nodes: MindMapNode[], baseSpacing: number = 120): number => {
//   if (nodes.length === 0) return baseSpacing

//   // 计算所有节点的平均高度
//   const totalHeight = nodes.reduce((sum, node) => sum + estimateNodeHeight(node), 0)
//   const avgHeight = totalHeight / nodes.length

//   // 基于平均高度动态调整间距，确保有足够的空间
//   // 增加更大的间距以避免重叠
//   return Math.max(baseSpacing, avgHeight + 80)
// }

// 计算子树的垂直边界（最小Y和最大Y）
const calculateSubtreeBounds = (nodeId: string, nodes: Record<string, MindMapNode>): { minY: number; maxY: number; height: number } => {
  const node = nodes[nodeId]
  if (!node) return { minY: 0, maxY: 0, height: 0 }

  if (node.children.length === 0) {
    const nodeHeight = estimateNodeHeight(node)
    return {
      minY: node.y - nodeHeight / 2,
      maxY: node.y + nodeHeight / 2,
      height: nodeHeight
    }
  }

  // 递归计算所有子节点的边界
  let minY = node.y
  let maxY = node.y

  node.children.forEach(childId => {
    const childBounds = calculateSubtreeBounds(childId, nodes)
    minY = Math.min(minY, childBounds.minY)
    maxY = Math.max(maxY, childBounds.maxY)
  })

  return {
    minY,
    maxY,
    height: maxY - minY
  }
}

// 重新计算所有节点位置 - 保持分支结构一致性
export const recalculateAllPositions = (mindMapNodes: Record<string, MindMapNode>) => {
  const updatedNodes = { ...mindMapNodes }

  // 第一步：按照标准思维导图布局计算所有位置
  const layoutBranch = (parentId: string, level: number) => {
    const parent = updatedNodes[parentId]
    if (!parent || parent.children.length === 0) return

    const childCount = parent.children.length
    const children = parent.children.map(id => updatedNodes[id]).filter(Boolean)
    const parentWidth = estimateNodeWidth(parent)
    const baseDistance = Math.max(250, parentWidth / 2 + 120)

    if (parent.id === "root") {
      // 根节点的子节点：左右分布
      const rightSideCount = Math.ceil(childCount / 2)
      const leftSideCount = Math.floor(childCount / 2)
      const rightSideChildren = children.slice(0, rightSideCount)
      const leftSideChildren = children.slice(rightSideCount)

      // 处理右侧节点
      if (rightSideChildren.length > 0) {
        const rightSpacing = Math.max(150, 100 + rightSideChildren.length * 20)
        const rightTotalHeight = (rightSideCount - 1) * rightSpacing
        const rightStartY = parent.y - rightTotalHeight / 2

        rightSideChildren.forEach((child, index) => {
          const childWidth = estimateNodeWidth(child)
          const distance = Math.max(baseDistance, parentWidth / 2 + childWidth / 2 + 100)

          updatedNodes[child.id] = {
            ...child,
            x: parent.x + distance,
            y: rightStartY + index * rightSpacing,
          }
        })
      }

      // 处理左侧节点
      if (leftSideChildren.length > 0) {
        const leftSpacing = Math.max(150, 100 + leftSideChildren.length * 20)
        const leftTotalHeight = (leftSideCount - 1) * leftSpacing
        const leftStartY = parent.y - leftTotalHeight / 2

        leftSideChildren.forEach((child, index) => {
          const childWidth = estimateNodeWidth(child)
          const distance = Math.max(baseDistance, parentWidth / 2 + childWidth / 2 + 100)

          updatedNodes[child.id] = {
            ...child,
            x: parent.x - distance,
            y: leftStartY + index * leftSpacing,
          }
        })
      }
    } else {
      // 分支节点的子节点：保持一致的内部结构
      const isParentOnRight = parent.x > updatedNodes["root"].x
      const enhancedSpacing = Math.max(120, 80 + childCount * 15)
      const totalHeight = (childCount - 1) * enhancedSpacing
      const startY = parent.y - totalHeight / 2

      children.forEach((child, index) => {
        const childWidth = estimateNodeWidth(child)
        const distance = Math.max(baseDistance, parentWidth / 2 + childWidth / 2 + 100)
        const newX = isParentOnRight ? parent.x + distance : parent.x - distance
        const newY = startY + index * enhancedSpacing

        updatedNodes[child.id] = {
          ...child,
          x: newX,
          y: newY,
        }
      })
    }

    // 递归处理子节点
    parent.children.forEach(childId => {
      layoutBranch(childId, level + 1)
    })
  }

  // 第一步：标准布局
  layoutBranch("root", 0)

  // 第二步：检测和解决分支间的重叠
  const rootNode = updatedNodes["root"]
  if (!rootNode) return updatedNodes

  const rightBranches: string[] = []
  const leftBranches: string[] = []

  // 分类左右分支
  rootNode.children.forEach(childId => {
    const child = updatedNodes[childId]
    if (child) {
      if (child.x > rootNode.x) {
        rightBranches.push(childId)
      } else {
        leftBranches.push(childId)
      }
    }
  })

  // 调整分支避免重叠
  const adjustBranchPositions = (branchIds: string[]) => {
    if (branchIds.length <= 1) return

    const branchBounds = branchIds.map(branchId => ({
      branchId,
      bounds: calculateSubtreeBounds(branchId, updatedNodes)
    }))

    // 按当前Y位置排序
    branchBounds.sort((a, b) => a.bounds.minY - b.bounds.minY)

    // 检查并调整重叠
    for (let i = 1; i < branchBounds.length; i++) {
      const currentBranch = branchBounds[i]
      const prevBranch = branchBounds[i - 1]

      // 如果当前分支与前一个分支重叠
      if (currentBranch.bounds.minY < prevBranch.bounds.maxY + 50) {
        const overlap = prevBranch.bounds.maxY + 50 - currentBranch.bounds.minY

        // 向下移动当前分支及其所有子树
        const moveSubtree = (nodeId: string, deltaY: number) => {
          const node = updatedNodes[nodeId]
          if (!node) return

          updatedNodes[nodeId] = {
            ...node,
            y: node.y + deltaY
          }

          // 递归移动所有子节点
          node.children.forEach(childId => {
            moveSubtree(childId, deltaY)
          })
        }

        moveSubtree(currentBranch.branchId, overlap)

        // 重新计算边界
        currentBranch.bounds = calculateSubtreeBounds(currentBranch.branchId, updatedNodes)
      }
    }
  }

  // 分别调整左右两侧的分支
  adjustBranchPositions(rightBranches)
  adjustBranchPositions(leftBranches)

  return updatedNodes
}





// 导出思维导图为JSON文件
export const exportMindMap = (mindMapNodes: Record<string, MindMapNode>) => {
  try {
    const dataStr = JSON.stringify(mindMapNodes, null, 2)
    const dataBlob = new Blob([dataStr], { type: "application/json" })
    const url = URL.createObjectURL(dataBlob)
    const link = document.createElement("a")
    link.href = url
    link.download = `mindmap-${new Date().toISOString().slice(0, 10)}.json`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  } catch (error) {
    console.error("导出思维导图失败:", error)
    alert("导出失败，请重试")
  }
}
