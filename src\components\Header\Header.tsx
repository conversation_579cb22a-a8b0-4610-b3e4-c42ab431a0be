import * as Tooltip from "@radix-ui/react-tooltip"
import React from "react"
import { FileControls } from "./FileControls"
import "./Header.css"
import { TabNavigation } from "./TabNavigation"
import type { HeaderProps } from "./types"
import { UserControls } from "./UserControls"

export const Header: React.FC<HeaderProps> = ({ activeTab, onTabChange }) => {
  return (
    <Tooltip.Provider delayDuration={100}>
      <header className="header">
        <div className="header-container">
          <FileControls />
          <TabNavigation activeTab={activeTab} onTabChange={onTabChange} />
          <UserControls />
        </div>
      </header>
    </Tooltip.Provider>
  )
}
